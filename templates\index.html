{% extends 'base.html' %}

{% block head %}
<!-- Any additional head content specific to this page -->
<!-- Chart.js -->
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<!-- Custom charts -->
<script src="{{ url_for('static', filename='js/charts.js') }}"></script>
{% endblock %}

{% block title %}Bac Simulateur | Calculez votre moyenne au baccalauréat 2025{% endblock %}

{% block description %}Simulateur gratuit pour calculer votre moyenne au bac 2025. Estimez vos chances d'obtenir une mention Tr<PERSON>, Bien ou Assez Bien pour tous types de bac (général, STMG, STL, pro).{% endblock %}

{% block schema_data %}
<script type="application/ld+json">
{
  "@context": "https://schema.org",
  "@type": "WebApplication",
  "name": "Bac Simulateur",
  "url": "https://bacsimulateur.online",
  "description": "Simulateur en ligne pour calculer sa moyenne au baccalauréat et estimer sa mention",
  "applicationCategory": "EducationalApplication",
  "operatingSystem": "All",
  "offers": {
    "@type": "Offer",
    "price": "0",
    "priceCurrency": "EUR"
  },
  "author": {
    "@type": "Organization",
    "name": "bacsimulateur.online",
    "url": "https://bacsimulateur.online/qui-sommes-nous"
  }
}
</script>
{% endblock %}

{% block content %}
<!-- Hero Section -->
<section class="hero-section text-center" id="hero">
  <div class="container">
    <h1 class="display-4 fw-bold mb-4">Simulateur de Notes du Bac</h1>
    <p class="lead mb-5">Calculez facilement votre moyenne au baccalauréat et estimez votre mention</p>
    <div class="d-flex justify-content-center gap-3 flex-wrap">
      <a href="#simulateur" class="btn btn-light btn-lg px-4 py-2">Utiliser le simulateur</a>
      <a href="/mentions" class="btn btn-outline-light btn-lg px-4 py-2">Découvrir les mentions</a>
    </div>
  </div>
</section>

<!-- Simulator Section -->
<section id="simulateur" class="calculator-section">
  <div class="container">
    <div class="text-center mb-5">
      <h2 class="fw-bold">Bac Simulateur - Calculez votre moyenne</h2>
      <p class="lead">Notre outil vous permet de simuler votre résultat au baccalauréat en fonction de vos notes actuelles et anticipées.</p>
    </div>
    
    <div class="row justify-content-center">
      <div class="col-lg-10">
        <div class="card shadow-sm">
          <div class="card-body p-4">
            <form id="bac-simulator">
              <div class="mb-4">
                <h3 class="h5 mb-3">Sélectionnez votre filière</h3>
                <select id="bac-type" class="form-select" required>
                  <option value="">Choisissez votre type de bac</option>
                  <option value="general">Bac Général</option>
                  <option value="techno">Bac Technologique</option>
                  <option value="stmg">Bac STMG</option>
                  <option value="stl">Bac STL</option>
                  <option value="pro">Bac Professionnel</option>
                  <option value="pro-agora">Bac Pro AGORA</option>
                  <option value="bfi">Bac BFI</option>
                </select>
              </div>
              
              <div id="coefficient-fields" class="mb-4">
                <!-- Les champs seront générés dynamiquement en JS -->
                <div class="alert alert-info">
                  <i class="fas fa-info-circle me-2"></i>Sélectionnez d'abord votre type de bac pour afficher les matières correspondantes.
                </div>
              </div>
              
              <div class="text-center">
                <button type="submit" class="btn btn-primary px-4 py-2">
                  <i class="fas fa-calculator me-2"></i>Calculer ma moyenne
                </button>
              </div>
            </form>
          </div>
        </div>
        
        <!-- Results Section -->
        <div id="simulation-results" class="mt-4" style="display: none;">
          <div class="results-summary">
            <h4 class="text-center mb-4">Résultats de votre simulation</h4>
            <div class="total-score">
              <span id="average-result">--</span>/20
            </div>
            <div class="text-center mb-3">
              <span class="badge bg-primary" id="mention-result">--</span>
            </div>
            <div class="text-center text-muted" id="points-difference">--</div>
          </div>

          <div class="chart-container">
            <canvas id="resultChart"></canvas>
          </div>

          <div class="row mt-4">
            <div class="col-md-6">
              <h5 class="mb-3">Détail par matière</h5>
              <div id="results-details" class="table-responsive">
                <table class="table table-sm">
                  <thead>
                    <tr>
                      <th>Matière</th>
                      <th>Note</th>
                      <th>Coef</th>
                      <th>Points</th>
                    </tr>
                  </thead>
                  <tbody id="results-table-body">
                    <!-- Sera rempli dynamiquement -->
                  </tbody>
                  <tfoot>
                    <tr class="table-primary">
                      <th colspan="2">Total</th>
                      <th id="total-coef">--</th>
                      <th id="total-points">--</th>
                    </tr>
                  </tfoot>
                </table>
              </div>
            </div>
          </div>
          
          <div class="mt-4 d-flex justify-content-between">
            <button id="recalculate-btn" class="btn btn-outline-secondary">
              <i class="fas fa-redo me-2"></i>Recalculer
            </button>
            <div>
              <button id="share-results" class="btn btn-outline-primary me-2">
                <i class="fas fa-share-alt me-2"></i>Partager
              </button>
              <button id="print-results" class="btn btn-outline-success">
                <i class="fas fa-print me-2"></i>Imprimer
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</section>

<!-- Bac Types Section -->
<section class="py-5 bg-white">
  <div class="container">
    <div class="text-center mb-5">
      <h2 class="fw-bold">Simulateurs par type de baccalauréat</h2>
      <p class="lead">Choisissez le simulateur adapté à votre parcours scolaire</p>
    </div>
    
    <div class="row g-4">
      <div class="col-md-6 col-lg-3">
        <div class="card h-100 shadow-sm">
          <div class="card-body p-4">
            <h3 class="h5 mb-3">Simulateur Bac Général</h3>
            <p class="card-text">Calculez votre moyenne pour le baccalauréat général avec les coefficients 2023-2024.</p>
            <div class="mt-auto pt-3">
              <a href="/simulateur-bac-general" class="btn btn-outline-primary d-block">Accéder</a>
            </div>
          </div>
        </div>
      </div>
      
      <div class="col-md-6 col-lg-3">
        <div class="card h-100 shadow-sm">
          <div class="card-body p-4">
            <h3 class="h5 mb-3">Simulateur Bac Techno</h3>
            <p class="card-text">Estimez votre moyenne pour les filières technologiques (STI2D, STMG, STL, etc.).</p>
            <div class="mt-auto pt-3">
              <a href="/simulateur-bac-techno" class="btn btn-outline-primary d-block">Accéder</a>
            </div>
          </div>
        </div>
      </div>
      
      <div class="col-md-6 col-lg-3">
        <div class="card h-100 shadow-sm">
          <div class="card-body p-4">
            <h3 class="h5 mb-3">Simulateur Bac STMG</h3>
            <p class="card-text">Spécialement conçu pour les élèves en Sciences et Technologies du Management et de la Gestion.</p>
            <div class="mt-auto pt-3">
              <a href="/simulateur-bac-stmg" class="btn btn-outline-primary d-block">Accéder</a>
            </div>
          </div>
        </div>
      </div>
      
      <div class="col-md-6 col-lg-3">
        <div class="card h-100 shadow-sm">
          <div class="card-body p-4">
            <h3 class="h5 mb-3">Simulateur Bac Pro</h3>
            <p class="card-text">Pour les élèves en baccalauréat professionnel, calculez vos chances de réussite.</p>
            <div class="mt-auto pt-3">
              <a href="/simulateur-bac-pro" class="btn btn-outline-primary d-block">Accéder</a>
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <div class="text-center mt-4">
      <a href="/simulateurs" class="btn btn-outline-secondary">
        <i class="fas fa-th-list me-2"></i>Voir tous nos simulateurs
      </a>
    </div>
  </div>
</section>

<!-- Mentions Section -->
<section class="py-5 bg-light">
  <div class="container">
    <div class="text-center mb-5">
      <h2 class="fw-bold">Les mentions au baccalauréat</h2>
      <p class="lead">Découvrez les différentes mentions et les moyennes requises</p>
    </div>
    
    <div class="row g-4">
      <div class="col-md-6 col-lg-3">
        <div class="card h-100 shadow-sm mention-card">
          <div class="card-body p-4">
            <h3 class="h5 mb-3 text-primary">Mention Très Bien</h3>
            <div class="mb-3">
              <span class="badge bg-primary p-2">Moyenne ≥ 16/20</span>
            </div>
            <p class="card-text">Une excellente réussite qui ouvre les portes des meilleures formations.</p>
          </div>
        </div>
      </div>
      
      <div class="col-md-6 col-lg-3">
        <div class="card h-100 shadow-sm mention-card">
          <div class="card-body p-4">
            <h3 class="h5 mb-3 text-primary">Mention Bien</h3>
            <div class="mb-3">
              <span class="badge bg-primary p-2">Moyenne ≥ 14/20 et < 16/20</span>
            </div>
            <p class="card-text">Une très bonne performance qui valorise votre dossier.</p>
          </div>
        </div>
      </div>
      
      <div class="col-md-6 col-lg-3">
        <div class="card h-100 shadow-sm mention-card">
          <div class="card-body p-4">
            <h3 class="h5 mb-3 text-primary">Mention Assez Bien</h3>
            <div class="mb-3">
              <span class="badge bg-primary p-2">Moyenne ≥ 12/20 et < 14/20</span>
            </div>
            <p class="card-text">Une mention qui témoigne d'un bon niveau général.</p>
          </div>
        </div>
      </div>
      
      <div class="col-md-6 col-lg-3">
        <div class="card h-100 shadow-sm mention-card">
          <div class="card-body p-4">
            <h3 class="h5 mb-3 text-primary">Baccalauréat</h3>
            <div class="mb-3">
              <span class="badge bg-primary p-2">Moyenne ≥ 10/20 et < 12/20</span>
            </div>
            <p class="card-text">La validation de vos études secondaires.</p>
          </div>
        </div>
      </div>
    </div>
    
    <div class="text-center mt-4">
      <a href="/mentions" class="btn btn-outline-primary">
        <i class="fas fa-award me-2"></i>En savoir plus sur les mentions
      </a>
    </div>
  </div>
</section>

<!-- Blog Section -->
<section class="py-5 bg-white">
  <div class="container">
    <div class="text-center mb-5">
      <h2 class="fw-bold">Articles et ressources pour votre bac</h2>
      <p class="lead">Découvrez nos guides et actualités pour vous aider à réussir votre baccalauréat</p>
    </div>
    
    <div class="row g-4">
      <div class="col-md-6 col-lg-3">
        <div class="card h-100 shadow-sm blog-card">
          <div class="card-body p-4">
            <div class="mb-3 text-primary">
              <i class="fas fa-book fs-4"></i>
            </div>
            <h3 class="h5 mb-3">Sujets du Bac</h3>
            <p class="card-text">Consultez les annales et préparez-vous efficacement aux épreuves.</p>
            <div class="mt-auto pt-3">
              <a href="#" class="btn btn-sm btn-outline-primary">Lire l'article</a>
            </div>
          </div>
        </div>
      </div>
      
      <div class="col-md-6 col-lg-3">
        <div class="card h-100 shadow-sm blog-card">
          <div class="card-body p-4">
            <div class="mb-3 text-primary">
              <i class="fas fa-calculator fs-4"></i>
            </div>
            <h3 class="h5 mb-3">Système de notation</h3>
            <p class="card-text">Comprendre le calcul des points et les coefficients pour chaque filière.</p>
            <div class="mt-auto pt-3">
              <a href="#" class="btn btn-sm btn-outline-primary">Lire l'article</a>
            </div>
          </div>
        </div>
      </div>
      
      <div class="col-md-6 col-lg-3">
        <div class="card h-100 shadow-sm blog-card">
          <div class="card-body p-4">
            <div class="mb-3 text-primary">
              <i class="fas fa-laptop fs-4"></i>
            </div>
            <h3 class="h5 mb-3">Plateforme Cyclades</h3>
            <p class="card-text">Guide pour accéder et utiliser la plateforme officielle d'inscription.</p>
            <div class="mt-auto pt-3">
              <a href="#" class="btn btn-sm btn-outline-primary">Lire l'article</a>
            </div>
          </div>
        </div>
      </div>
      
      <div class="col-md-6 col-lg-3">
        <div class="card h-100 shadow-sm blog-card">
          <div class="card-body p-4">
            <div class="mb-3 text-primary">
              <i class="fas fa-newspaper fs-4"></i>
            </div>
            <h3 class="h5 mb-3">Actualités du Bac</h3>
            <p class="card-text">Restez informé des dernières nouvelles concernant le baccalauréat.</p>
            <div class="mt-auto pt-3">
              <a href="#" class="btn btn-sm btn-outline-primary">Voir toutes les actualités</a>
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <div class="text-center mt-4">
      <a href="#" class="btn btn-outline-secondary">
        <i class="fas fa-th-list me-2"></i>Voir tous les articles
      </a>
    </div>
  </div>
</section>

<!-- Testimonials Section -->
<section class="py-5 bg-light">
  <div class="container">
    <div class="text-center mb-5">
      <h2 class="fw-bold">Ils ont utilisé notre simulateur</h2>
      <p class="lead">Découvrez les témoignages des étudiants qui ont préparé leur bac avec notre outil</p>
    </div>
    
    <div class="row justify-content-center">
      <div class="col-lg-10">
        <div id="testimonialCarousel" class="carousel slide" data-bs-ride="carousel">
          <div class="carousel-inner">
            <div class="carousel-item active">
              <div class="testimonial p-4">
                <div class="row">
                  <div class="col-md-3 text-center mb-4 mb-md-0">
                    <div class="rounded-circle bg-primary text-white d-flex align-items-center justify-content-center mx-auto mb-3" style="width: 80px; height: 80px;">
                      <span class="h3 mb-0">S</span>
                    </div>
                    <h4 class="h6 mb-1">Sophie</h4>
                    <p class="small text-muted">Terminale Générale</p>
                  </div>
                  <div class="col-md-9">
                    <p class="lead">"Grâce au simulateur de bacsimulateur.online, j'ai pu anticiper ma mention Très Bien et me concentrer sur les matières à améliorer."</p>
                    <div class="text-warning">
                      <i class="fas fa-star"></i>
                      <i class="fas fa-star"></i>
                      <i class="fas fa-star"></i>
                      <i class="fas fa-star"></i>
                      <i class="fas fa-star"></i>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div class="carousel-item">
              <div class="testimonial p-4">
                <div class="row">
                  <div class="col-md-3 text-center mb-4 mb-md-0">
                    <div class="rounded-circle bg-primary text-white d-flex align-items-center justify-content-center mx-auto mb-3" style="width: 80px; height: 80px;">
                      <span class="h3 mb-0">T</span>
                    </div>
                    <h4 class="h6 mb-1">Thomas</h4>
                    <p class="small text-muted">Terminale STMG</p>
                  </div>
                  <div class="col-md-9">
                    <p class="lead">"Un outil précis et facile à utiliser qui m'a aidé à gérer mon stress avant les épreuves du bac STMG."</p>
                    <div class="text-warning">
                      <i class="fas fa-star"></i>
                      <i class="fas fa-star"></i>
                      <i class="fas fa-star"></i>
                      <i class="fas fa-star"></i>
                      <i class="fas fa-star-half-alt"></i>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div class="carousel-item">
              <div class="testimonial p-4">
                <div class="row">
                  <div class="col-md-3 text-center mb-4 mb-md-0">
                    <div class="rounded-circle bg-primary text-white d-flex align-items-center justify-content-center mx-auto mb-3" style="width: 80px; height: 80px;">
                      <span class="h3 mb-0">E</span>
                    </div>
                    <h4 class="h6 mb-1">Emma</h4>
                    <p class="small text-muted">Terminale Bac Pro</p>
                  </div>
                  <div class="col-md-9">
                    <p class="lead">"Le simulateur Bac Pro m'a permis de mieux comprendre les coefficients et d'optimiser mes révisions."</p>
                    <div class="text-warning">
                      <i class="fas fa-star"></i>
                      <i class="fas fa-star"></i>
                      <i class="fas fa-star"></i>
                      <i class="fas fa-star"></i>
                      <i class="fas fa-star"></i>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <button class="carousel-control-prev" type="button" data-bs-target="#testimonialCarousel" data-bs-slide="prev">
            <span class="carousel-control-prev-icon" aria-hidden="true"></span>
            <span class="visually-hidden">Précédent</span>
          </button>
          <button class="carousel-control-next" type="button" data-bs-target="#testimonialCarousel" data-bs-slide="next">
            <span class="carousel-control-next-icon" aria-hidden="true"></span>
            <span class="visually-hidden">Suivant</span>
          </button>
        </div>
      </div>
    </div>
  </div>
</section>

<!-- FAQ Section -->
<section class="py-5 bg-white">
  <div class="container">
    <div class="text-center mb-5">
      <h2 class="fw-bold">Questions fréquentes sur le bac</h2>
      <p class="lead">Trouvez les réponses aux questions les plus courantes sur le baccalauréat</p>
    </div>
    
    <div class="row justify-content-center">
      <div class="col-lg-10">
        <div class="accordion" id="faqAccordion">
          <div class="accordion-item shadow-sm mb-3">
            <h3 class="accordion-header" id="headingOne">
              <button class="accordion-button" type="button" data-bs-toggle="collapse" data-bs-target="#collapseOne" aria-expanded="true" aria-controls="collapseOne">
                Comment calculer ma moyenne au bac ?
              </button>
            </h3>
            <div id="collapseOne" class="accordion-collapse collapse show" aria-labelledby="headingOne" data-bs-parent="#faqAccordion">
              <div class="accordion-body">
                <p>La moyenne au baccalauréat est calculée en fonction des notes obtenues aux différentes épreuves, pondérées par leurs coefficients respectifs. Notre simulateur bac vous permet d'effectuer ce calcul automatiquement.</p>
                <p>La formule est la suivante : <strong>Moyenne = Somme(Note × Coefficient) / Somme(Coefficient)</strong></p>
              </div>
            </div>
          </div>
          
          <div class="accordion-item shadow-sm mb-3">
            <h3 class="accordion-header" id="headingTwo">
              <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapseTwo" aria-expanded="false" aria-controls="collapseTwo">
                Quelles sont les mentions possibles au bac ?
              </button>
            </h3>
            <div id="collapseTwo" class="accordion-collapse collapse" aria-labelledby="headingTwo" data-bs-parent="#faqAccordion">
              <div class="accordion-body">
                <p>Les mentions au baccalauréat sont attribuées en fonction de votre moyenne générale :</p>
                <ul>
                  <li><strong>Assez Bien</strong> : Moyenne entre 12/20 et 13,99/20</li>
                  <li><strong>Bien</strong> : Moyenne entre 14/20 et 15,99/20</li>
                  <li><strong>Très Bien</strong> : Moyenne égale ou supérieure à 16/20</li>
                </ul>
                <p>Notre simulateur vous indique la mention que vous pourriez obtenir en fonction de vos notes.</p>
              </div>
            </div>
          </div>
          
          <div class="accordion-item shadow-sm mb-3">
            <h3 class="accordion-header" id="headingThree">
              <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapseThree" aria-expanded="false" aria-controls="collapseThree">
                Comment fonctionnent les points d'avance au bac ?
              </button>
            </h3>
            <div id="collapseThree" class="accordion-collapse collapse" aria-labelledby="headingThree" data-bs-parent="#faqAccordion">
              <div class="accordion-body">
                <p>Les points d'avance correspondent aux points au-dessus de la moyenne de 10/20 multipliés par le total des coefficients. Ils peuvent compenser des notes plus faibles dans certaines matières.</p>
                <p>Par exemple, si vous avez 12/20 de moyenne générale avec un total de coefficients de 100, vous avez (12-10) × 100 = 200 points d'avance.</p>
              </div>
            </div>
          </div>
          
          <div class="accordion-item shadow-sm">
            <h3 class="accordion-header" id="headingFour">
              <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapseFour" aria-expanded="false" aria-controls="collapseFour">
                Où trouver les sujets du bac des années précédentes ?
              </button>
            </h3>
            <div id="collapseFour" class="accordion-collapse collapse" aria-labelledby="headingFour" data-bs-parent="#faqAccordion">
              <div class="accordion-body">
                <p>Vous pouvez consulter notre section <a href="#" class="text-decoration-none">Sujets Bac</a> qui regroupe les annales des différentes filières, ou visiter le site officiel de l'Éducation Nationale.</p>
                <p>Les annales sont un excellent moyen de se préparer aux épreuves et de comprendre le format des questions posées.</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</section>

<!-- CTA Section -->
<section class="py-5 bg-primary text-white">
  <div class="container">
    <div class="row py-3 justify-content-center text-center">
      <div class="col-lg-8">
        <h2 class="fw-bold mb-3">Prêt à calculer votre moyenne au bac ?</h2>
        <p class="lead mb-4">Utilisez notre simulateur gratuit pour estimer votre moyenne et découvrir votre mention potentielle</p>
        <a href="#simulateur" class="btn btn-light btn-lg px-4 py-2">
          <i class="fas fa-calculator me-2"></i>Calculer ma moyenne maintenant
        </a>
      </div>
    </div>
  </div>
</section>
{% endblock %}

{% block extra_js %}
<script>
  // Fonction pour afficher les champs de notes en fonction du type de bac
  document.addEventListener('DOMContentLoaded', function() {
    const bacTypeSelect = document.getElementById('bac-type');
    const coefficientFields = document.getElementById('coefficient-fields');
    const simulatorForm = document.getElementById('bac-simulator');
    const simulationResults = document.getElementById('simulation-results');
    
    // Matières par type de bac (exemple - à compléter avec les vraies matières et coefficients)
    const subjectsByType = {
      'general': [
        {name: "Français (écrit)", coef: 5},
        {name: "Français (oral)", coef: 5},
        {name: "Philosophie", coef: 8},
        {name: "Grand oral", coef: 10},
        {name: "Spécialité 1", coef: 16},
        {name: "Spécialité 2", coef: 16},
        {name: "Histoire-géographie", coef: 6},
        {name: "Enseignement scientifique", coef: 6},
        {name: "LVA", coef: 6},
        {name: "LVB", coef: 6},
        {name: "EPS", coef: 6},
        {name: "Enseignement de spécialité de première", coef: 8},
        {name: "EMC", coef: 2}
      ],
      'stmg': [
        {name: "Français (écrit)", coef: 5},
        {name: "Français (oral)", coef: 5},
        {name: "Philosophie", coef: 4},
        {name: "Grand oral", coef: 14},
        {name: "Management, sciences de gestion et numérique", coef: 16},
        {name: "Économie-droit", coef: 16},
        {name: "Histoire-géographie", coef: 6},
        {name: "Mathématiques", coef: 6},
        {name: "LVA", coef: 6},
        {name: "LVB", coef: 6},
        {name: "EPS", coef: 6},
        {name: "Enseignement de spécialité de première", coef: 8},
        {name: "EMC", coef: 2}
      ],
      // Ajouter d'autres types de bac...
    };
    
    // Par défaut pour les autres types non définis explicitement
    const defaultSubjects = [
      {name: "Français (écrit)", coef: 5},
      {name: "Français (oral)", coef: 5},
      {name: "Épreuve principale 1", coef: 16},
      {name: "Épreuve principale 2", coef: 16},
      {name: "Épreuve commune 1", coef: 6},
      {name: "Épreuve commune 2", coef: 6},
      {name: "Épreuve commune 3", coef: 6},
      {name: "Option 1", coef: 2}
    ];
    
    // Fonction pour générer les champs de saisie des notes
    function generateSubjectFields(subjects) {
      let html = '<div class="row g-3">';
      
      subjects.forEach((subject, index) => {
        html += `
          <div class="col-md-6 col-lg-4">
            <div class="form-group">
              <label for="subject-${index}" class="form-label">${subject.name} (coef. ${subject.coef})</label>
              <input type="number" class="form-control" id="subject-${index}" 
                     name="subject-${index}" min="0" max="20" step="0.25" 
                     placeholder="Note /20" data-coef="${subject.coef}">
            </div>
          </div>
        `;
      });
      
      html += '</div>';
      return html;
    }
    
    // Événement de changement du type de bac
    if (bacTypeSelect) {
      bacTypeSelect.addEventListener('change', function() {
        const selectedType = this.value;
        if (selectedType) {
          const subjects = subjectsByType[selectedType] || defaultSubjects;
          coefficientFields.innerHTML = generateSubjectFields(subjects);
        }
      });
    }
    
    // Événement de soumission du formulaire
    if (simulatorForm) {
      simulatorForm.addEventListener('submit', function(e) {
        e.preventDefault();
        
        // Calcul de la moyenne
        let totalPoints = 0;
        let totalCoefficients = 0;
        let results = [];
        let chartData = [];
        
        // Récupérer toutes les notes saisies
        const inputs = coefficientFields.querySelectorAll('input[type="number"]');
        inputs.forEach(input => {
          const note = parseFloat(input.value) || 0;
          const coef = parseInt(input.dataset.coef) || 0;
          const points = note * coef;
          
          totalPoints += points;
          totalCoefficients += coef;
          
          // Stocker les résultats pour affichage détaillé
          if (input.value) {
            const subjectName = input.previousElementSibling.textContent.replace(/ \(coef. \d+\)$/, '');
            results.push({
              subject: subjectName,
              note: note,
              coef: coef,
              points: points
            });
            chartData.push(note);
          }
        });
        
        // Calculer la moyenne
        const average = totalCoefficients > 0 ? totalPoints / totalCoefficients : 0;
        
        // Déterminer la mention
        let mention = "";
        if (average >= 16) {
          mention = "Mention Très Bien";
        } else if (average >= 14) {
          mention = "Mention Bien";
        } else if (average >= 12) {
          mention = "Mention Assez Bien";
        } else if (average >= 10) {
          mention = "Admis";
        } else {
          mention = "Non admis";
        }
        
        // Calculer les points d'avance ou manquants
        const pointsDifference = (average - 10) * totalCoefficients;
        const pointsText = pointsDifference >= 0 ? 
          `+${pointsDifference.toFixed(1)} points d'avance` : 
          `${pointsDifference.toFixed(1)} points manquants`;
        
        // Afficher les résultats
        document.getElementById('average-result').textContent = average.toFixed(2);
        document.getElementById('mention-result').textContent = mention;
        document.getElementById('points-difference').textContent = pointsText;
        
        // Mettre à jour le tableau des résultats
        const tableBody = document.getElementById('results-table-body');
        tableBody.innerHTML = '';
        results.forEach(result => {
          const row = document.createElement('tr');
          row.innerHTML = `
            <td>${result.subject}</td>
            <td>${result.note.toFixed(2)}</td>
            <td>${result.coef}</td>
            <td>${result.points.toFixed(2)}</td>
          `;
          tableBody.appendChild(row);
        });
        
        // Mettre à jour les totaux
        document.getElementById('total-coef').textContent = totalCoefficients;
        document.getElementById('total-points').textContent = totalPoints.toFixed(2);
        
        // Mettre à jour le graphique
        updateChart(chartData);
        
        // Afficher la section des résultats
        simulationResults.style.display = 'block';
        
        // Scroll vers les résultats
        simulationResults.scrollIntoView({ behavior: 'smooth' });
      });
    }
    
    // Événement pour le bouton de recalcul
    const recalculateBtn = document.getElementById('recalculate-btn');
    if (recalculateBtn) {
      recalculateBtn.addEventListener('click', function() {
        simulationResults.style.display = 'none';
        simulatorForm.reset();
        coefficientFields.innerHTML = '<div class="alert alert-info"><i class="fas fa-info-circle me-2"></i>Sélectionnez d\'abord votre type de bac pour afficher les matières correspondantes.</div>';
      });
    }
    
    // Événement pour le bouton imprimer
    const printResultsBtn = document.getElementById('print-results');
    if (printResultsBtn) {
      printResultsBtn.addEventListener('click', function() {
        window.print();
      });
    }
    
    // Événement pour le bouton partager
    const shareResultsBtn = document.getElementById('share-results');
    if (shareResultsBtn) {
      shareResultsBtn.addEventListener('click', function() {
        const average = document.getElementById('average-result').textContent;
        const mention = document.getElementById('mention-result').textContent;
        const shareText = `J'ai obtenu une moyenne de ${average}/20 (${mention}) au bac selon le simulateur de bacsimulateur.online !`;
        
        // Essayer de partager via l'API Web Share si disponible
        if (navigator.share) {
          navigator.share({
            title: 'Mes résultats de simulation du bac',
            text: shareText,
            url: window.location.href
          });
        } else {
          // Fallback: copier dans le presse-papier
          navigator.clipboard.writeText(shareText + ' ' + window.location.href)
            .then(() => {
              alert('Résultats copiés dans le presse-papier !');
            });
        }
      });
    }
  });
</script>
{% endblock %} 